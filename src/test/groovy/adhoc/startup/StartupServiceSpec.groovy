package adhoc.startup

import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.util.ReflectionTestUtils
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.websocket.events.NewHeadsNotification
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest
import software.amazon.awssdk.services.dynamodb.model.ScanRequest
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import spock.lang.Shared
import spock.lang.Specification

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class StartupServiceSpec extends Specification {

	@Shared
	DynamoDbClient dynamoDbClient

	@Shared
	S3Client s3Client

	@Shared
	def logger = LoggerFactory.getLogger(LoggingService.class) as Logger

	@Autowired
	LoggingService loggingService

	@Autowired
	DownloadAbiService downloadAbiService

	@Autowired
	MonitorEventService monitorEventService

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties config

	@Autowired
	BlockHeightDao blockHeightDao

	static final String TEST_BUCKET = "abijson-local-bucket"  // Same as default in application.properties
	static final String EVENTS_TABLE = "local-Events"        // Same as default in application.properties
	static final String BLOCK_HEIGHT_TABLE = "local-BlockHeight"  // Same as default in application.properties

	def logAppender = new ListAppender<ILoggingEvent>()
	def web3j = Mock(Web3j)
	def scheduler = Executors.newScheduledThreadPool(1)


	@DynamicPropertySource
	static void configureProperties(DynamicPropertyRegistry registry) {
		registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
		registry.add("eagerStart", { "false" })
		registry.add("aws.dynamodb.table-prefix", { "" })
	}

	def setupSpec() {
		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables and bucket
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
	}

	def cleanupSpec() {
		dynamoDbClient?.close()
		s3Client?.close()
	}

	def setup() {
		// Clear all S3 bucket contents completely
		clearS3BucketCompletely()
		// Upload real ABI files to
		AdhocHelper.uploadRealAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])
		// Clear all DynamoDB table contents
		clearDynamoDBTables()
		// Setup web3j mock
		setupWeb3jMock()
		// Start log appender to capture logs
		logAppender.start()
		logger.addAppender(logAppender)
		AdhocHelper.resetRunningFlag()
	}

	private void setupWeb3jMock() {

		def field = Web3jConfig.class.getDeclaredField("web3j")
		field.setAccessible(true)
		field.set(web3jConfig, web3j)
		println("Web3j mock setup completed")
	}

	private void setUpEventStream(List<NewHeadsNotification> blocks) {
		def processor = PublishProcessor.<NewHeadsNotification> create()
		def index = new AtomicInteger(0)
		scheduler.scheduleAtFixedRate({
			int i = index.getAndIncrement()
			if (i < blocks.size()) {
				processor.onNext(blocks.get(i))
			}
		}, 0, 2, TimeUnit.SECONDS)

		web3j.newHeadsNotifications() >> Flowable.fromPublisher(processor)
	}

	private void setUpPendingEvent(List<EthLog.LogResult> resultList) {
		def mockRequest = Mock(Request)
		def mockLog = Mock(EthLog)

		web3j.ethGetLogs(_ as EthFilter) >> mockRequest
		mockRequest.send() >> mockLog
		mockLog.getLogs() >> resultList
	}

	def cleanup() {
		// Clear S3 bucket for next test
		clearS3Bucket()
		// Shut down the scheduler to stop mock event generation
		scheduler.shutdown()
		scheduler.awaitTermination(5, TimeUnit.SECONDS)
	}

	private void clearS3Bucket() {
		clearS3BucketCompletely()
	}

	private void clearS3BucketCompletely() {
		try {
			String continuationToken = null
			boolean hasMoreObjects = true

			while (hasMoreObjects) {
				def listRequest = ListObjectsV2Request.builder()
						.bucket(TEST_BUCKET)
						.continuationToken(continuationToken)
						.build()

				def listResponse = s3Client.listObjectsV2(listRequest as ListObjectsV2Request)

				listResponse.contents().each { obj ->
					s3Client.deleteObject(DeleteObjectRequest.builder()
							.bucket(TEST_BUCKET)
							.key(obj.key())
							.build() as DeleteObjectRequest)
				}

				continuationToken = listResponse.nextContinuationToken()
				hasMoreObjects = (continuationToken != null)
			}

			def finalCheck = s3Client.listObjectsV2 {
				it.bucket(TEST_BUCKET)
			}
			if (finalCheck.contents().isEmpty()) {
				println("S3 bucket ${TEST_BUCKET} successfully cleared")
			} else {
				println("Warning: S3 bucket ${TEST_BUCKET} still contains ${finalCheck.contents().size()} objects")
			}
		} catch (Exception e) {
			println("Error clearing S3 bucket: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTables() {
		try {
			// Clear events table
			clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

			// Clear block height table
			clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
		} catch (Exception e) {
			println("Error clearing DynamoDB tables: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
		try {
			// Scan the table to get all items
			def scanRequest = ScanRequest.builder()
					.tableName(tableName)
					.build()

			def scanResponse = dynamoDbClient.scan(scanRequest as ScanRequest)

			// Delete each item
			scanResponse.items().each { item ->
				def keyMap = [:]
				keyAttributes.each { keyAttr ->
					if (item.containsKey(keyAttr)) {
						keyMap[keyAttr] = item[keyAttr]
					}
				}

				if (!keyMap.isEmpty()) {
					def deleteRequest = DeleteItemRequest.builder()
							.tableName(tableName)
							.key(keyMap as Map<String, AttributeValue>)
							.build()
					dynamoDbClient.deleteItem(deleteRequest as DeleteItemRequest)
				}
			}
		} catch (Exception e) {
			println("Error clearing DynamoDB table ${tableName}: ${e.message}")
		}
	}

	/**
	 * Successful Service Startup
	 * Verifies service starts successfully with all dependencies available
	 * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
	 */
	def "Should start successfully with all dependencies available"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
		when: "Testing real service startup with CommandLineRunner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "Service should start successfully and log the required messages"
		noExceptionThrown()
		logAppender.list*.formattedMessage.contains("Started bc monitoring")
		and: "Real services should be available"
		assert downloadAbiService
		assert monitorEventService
		assert loggingService
		assert s3Client
		assert dynamoDbClient
	}

	/**
	 * Service reinitialize after error occurs
	 * Verifies service automatically reinitialize monitoring after error occurs
	 * Expected: Service reinitialize and logs "Restarting bc monitoring"
	 */
	def "Should automatically reinitialize monitoring if error occurs"() {
		given: "Valid environment with accessible dependencies"

		//Not setup websocket event stream and pending event

		when: "Testing real service startup with CommandLineRunner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert loggingService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		logAppender.list*.formattedMessage.contains("Restarting bc monitoring")
	}

	/**
	 * Service Startup with Empty ABI Bucket
	 * Verifies service starts successfully when S3 bucket exists but contains no ABI files
	 * Expected: Service starts with no contract addresses loaded, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty ABI bucket"() {
		given: "An empty S3 bucket and all dependencies available"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
		clearS3Bucket()
		assert s3Client.listObjectsV2 { it.bucket(TEST_BUCKET) }.contents().isEmpty()

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert loggingService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Get blockheight: 0") }
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Service startup with DynamoDB BlockHeight table is empty
	 * Verifies service starts successfully
	 * Expected: Service starts monitoring from block 0, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty DynamoDB BlockHeight table"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
		clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
		assert dynamoDbClient.scan { it.tableName(BLOCK_HEIGHT_TABLE) }.items().isEmpty()

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert loggingService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Get blockheight: 0") }
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Should fails to start when required properties are invalid
	 * Verifies service fails
	 * Expected: Service logs "Error starting bc monitoring"
	 */
	def "Fails to start when required properties are invalid"() {
		given:
		config.aws.s3.setBucketName(null)
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")
		config.aws.s3.setBucketName(TEST_BUCKET)
		then:
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error starting bc monitoring") }
	}

	/**
	 * Should restart if DynamoDB connection Error
	 * Verifies restart application
	 * Expected: Service logs "Failed to get blockheight:" and "Restarting bc monitoring"
	 */
	def "Should restart if DynamoDB connection Error"() {
		given: "InValid DynamoDB connection"
		def invalidDynamo = DynamoDbClient.builder()
				.endpointOverride(URI.create("http://localhost:100"))
				.credentialsProvider(dynamoDbClient.serviceClientConfiguration().credentialsProvider())
				.region(dynamoDbClient.serviceClientConfiguration().region())
				.build()

		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
		ReflectionTestUtils.setField(blockHeightDao, "dynamoDbClient", invalidDynamo)
		when: "Testing real service startup with CommandLineRunner"
		// Get the CommandLineRunner bean and execute it to trigger the logs
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
		scheduler.schedule( {
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")
		// Reset the DynamoDB client to the valid one after the test
		ReflectionTestUtils.setField(blockHeightDao, "dynamoDbClient", dynamoDbClient)
		then: "Service should keep restart due to DynamoDB connection error"
		logAppender.list.any { it.formattedMessage.contains("Failed to get blockheight:") }
		logAppender.list*.formattedMessage.contains("Restarting bc monitoring")
	}
}
