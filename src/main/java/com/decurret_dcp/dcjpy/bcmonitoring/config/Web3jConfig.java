package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.websocket.WebSocketService;

@Configuration
@RequiredArgsConstructor
public class Web3jConfig {

  /** Exception thrown when a Web3j connection fails. */
  public static class Web3jConnectionException extends RuntimeException {
    public Web3jConnectionException(String message) {
      super(message);
    }

    public Web3jConnectionException(String message, Throwable cause) {
      super(message, cause);
    }
  }

  private static final String WEBSOCKET_URI_TEMPLATE = "ws://%s:%s";

  private final BcmonitoringConfigurationProperties properties;
  private final LoggingService logger;
  private Web3j web3j;

  /**
   * Gets the cached Web3j instance, creating it if necessary.
   *
   * @return The cached Web3j instance
   * @throws Web3jConnectionException if connection fails
   */
  public synchronized Web3j getWeb3j() throws Web3jConnectionException {
    if (web3j == null) {
      web3j = createWebSocketWeb3j();
    }
    return web3j;
  }

  /**
   * Creates a new WebSocket Web3j connection. This method should be used for each operation that
   * requires a fresh connection.
   *
   * @return A new Web3j instance connected via WebSocket
   * @throws Web3jConnectionException if the connection fails
   */
  public synchronized Web3j createWebSocketWeb3j() throws Web3jConnectionException {
    try {
      String wsEndpoint =
          String.format(
              WEBSOCKET_URI_TEMPLATE,
              properties.getWebsocket().getUri().getHost(),
              properties.getWebsocket().getUri().getPort());

      WebSocketService webSocketService = new WebSocketService(wsEndpoint, true);
      webSocketService.connect();

      return Web3j.build(webSocketService);
    } catch (Exception e) {
      logger.error("Failed to create WebSocket Web3j connection", e);
      throw new Web3jConnectionException("Failed to create WebSocket Web3j connection", e);
    }
  }

  /**
   * Shuts down the cached Web3j instance.
   *
   * @throws Web3jConnectionException if the shutdown fails
   */
  public synchronized void shutdownWeb3j() {
    if (web3j != null) {
      try {
        web3j.shutdown();
        web3j = null;
        logger.info("Successfully shut down cached Web3j connection");
      } catch (Exception e) {
        logger.error("Error shutting down cached Web3j connection", e);
      }
    }
  }
}
